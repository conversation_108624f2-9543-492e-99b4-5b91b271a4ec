{"name": "mindtrack", "version": "1.0.0", "description": "", "main": "index.js", "directories": {"lib": "lib"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "next dev", "build": "next build", "start": "next start", "deploy": "vercel"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@supabase/supabase-js": "^2.52.1", "mongodb": "^6.18.0", "next": "^15.4.4", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^3.4.1"}}